package com.logictrue.word.service;

import com.logictrue.word.dto.CheckRecordExportRequest;
import com.logictrue.word.dto.JsonTableExportRequest;
import com.logictrue.word.entity.DesignWord;
import com.logictrue.word.service.IDesignWordService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 检验记录导出服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CheckRecordExportService {

    private final IDesignWordService designWordService;
    private final WordExportService wordExportService;
    private final ObjectMapper objectMapper;

    /**
     * 导出检验记录Word文档
     */
    public byte[] exportCheckRecord(CheckRecordExportRequest request) throws IOException {
        log.info("开始导出检验记录，车辆ID: {}, 导出类型: {}", request.getCarId(), request.getExportType());

        // 根据导出类型处理
        switch (request.getExportType()) {
            case "current_page":
                return exportCurrentPage(request);
            case "all_pages":
                return exportAllPages(request);
            case "batch":
                return exportBatch(request);
            default:
                throw new IllegalArgumentException("不支持的导出类型: " + request.getExportType());
        }
    }

    /**
     * 导出当前页面
     */
    private byte[] exportCurrentPage(CheckRecordExportRequest request) throws IOException {
        log.info("导出当前页面，车辆ID: {}, 页面顺序: {}", request.getCarId(), request.getPageOrder());

        // 获取页面设计数据
        DesignWord designWord = designWordService.selectDesignWordByCarIdAndPage(
                request.getCarId(), request.getPageOrder());

        if (designWord == null) {
            throw new RuntimeException("未找到指定页面的设计数据");
        }

        // 获取检验记录数据
        List<Map<String, Object>> checkRecords = getCheckRecordData(request.getCarId());

        // 构建导出数据
        JsonTableExportRequest exportRequest = buildExportRequest(
                Arrays.asList(designWord), checkRecords, request);

        // 设置文档标题
        String title = request.getTitle() != null ? request.getTitle() :
                (designWord.getTitle() + "_第" + request.getPageOrder() + "页");
        exportRequest.setTitle(title);

        return wordExportService.exportNewJsonFormatToWord(exportRequest);
    }

    /**
     * 导出全部页面
     */
    private byte[] exportAllPages(CheckRecordExportRequest request) throws IOException {
        log.info("导出全部页面，车辆ID: {}", request.getCarId());

        // 获取所有页面设计数据
        List<DesignWord> designWords = designWordService.selectDesignWordPagesByCarId(request.getCarId());

        if (designWords.isEmpty()) {
            throw new RuntimeException("未找到指定车辆的设计数据");
        }

        // 按页面顺序排序
        designWords.sort(Comparator.comparing(DesignWord::getPageOrder));

        // 获取检验记录数据
        List<Map<String, Object>> checkRecords = getCheckRecordData(request.getCarId());

        // 构建导出数据
        JsonTableExportRequest exportRequest = buildExportRequest(designWords, checkRecords, request);

        // 设置文档标题
        String title = request.getTitle() != null ? request.getTitle() :
                (designWords.get(0).getTitle() + "_全部页面");
        exportRequest.setTitle(title);

        return wordExportService.exportNewJsonFormatToWord(exportRequest);
    }

    /**
     * 批量导出
     */
    private byte[] exportBatch(CheckRecordExportRequest request) throws IOException {
        log.info("批量导出，车辆ID列表: {}", request.getCarIds());

        List<DesignWord> allDesignWords = new ArrayList<>();
        List<Map<String, Object>> allCheckRecords = new ArrayList<>();

        // 遍历所有车辆ID
        for (String carId : request.getCarIds()) {
            // 获取页面设计数据
            List<DesignWord> designWords = designWordService.selectDesignWordPagesByCarId(carId);
            if (!designWords.isEmpty()) {
                designWords.sort(Comparator.comparing(DesignWord::getPageOrder));
                allDesignWords.addAll(designWords);

                // 获取检验记录数据
                List<Map<String, Object>> checkRecords = getCheckRecordData(carId);
                allCheckRecords.addAll(checkRecords);
            }
        }

        if (allDesignWords.isEmpty()) {
            throw new RuntimeException("未找到任何车辆的设计数据");
        }

        // 构建导出数据
        JsonTableExportRequest exportRequest = buildExportRequest(allDesignWords, allCheckRecords, request);

        // 设置文档标题
        String title = request.getTitle() != null ? request.getTitle() : "检验记录表_批量导出";
        exportRequest.setTitle(title);

        return wordExportService.exportNewJsonFormatToWord(exportRequest);
    }

    /**
     * 获取检验记录数据（模拟数据，实际应该从数据库查询）
     */
    private List<Map<String, Object>> getCheckRecordData(String carId) {
        log.info("查询检验记录数据，车辆ID: {}", carId);

        List<Map<String, Object>> records = new ArrayList<>();

        // 模拟数据，实际应该从数据库查询
        if ("0822".equals(carId)) {
            Map<String, Object> record1 = new HashMap<>();
            record1.put("check_name", "脱漆");
            record1.put("check_content", "1.去除浮漆");
            record1.put("result", "完成");
            record1.put("month_str", 8);
            record1.put("day_str", 22);
            record1.put("check_user_name", "张三");
            record1.put("bzz", "张三");
            record1.put("jyy", "张三");
            records.add(record1);

            Map<String, Object> record2 = new HashMap<>();
            record2.put("check_name", "脱漆");
            record2.put("check_content", "2.用水冲洗");
            record2.put("result", "完成");
            record2.put("month_str", 8);
            record2.put("day_str", 22);
            record2.put("check_user_name", "张三");
            record2.put("bzz", "张三");
            record2.put("jyy", "张三");
            records.add(record2);

            Map<String, Object> record3 = new HashMap<>();
            record3.put("check_name", "打磨");
            record3.put("check_content", "1.表面打磨处理");
            record3.put("result", "进行中");
            record3.put("month_str", 8);
            record3.put("day_str", 23);
            record3.put("check_user_name", "李四");
            record3.put("bzz", "李四");
            record3.put("jyy", "王五");
            records.add(record3);

            Map<String, Object> record4 = new HashMap<>();
            record4.put("check_name", "喷漆");
            record4.put("check_content", "1.底漆喷涂");
            record4.put("result", "待开始");
            record4.put("month_str", 8);
            record4.put("day_str", 24);
            record4.put("check_user_name", "赵六");
            record4.put("bzz", "赵六");
            record4.put("jyy", "钱七");
            records.add(record4);
        }

        log.info("查询到检验记录数据 {} 条", records.size());
        return records;
    }

    /**
     * 构建导出请求数据
     */
    private JsonTableExportRequest buildExportRequest(
            List<DesignWord> designWords,
            List<Map<String, Object>> checkRecords,
            CheckRecordExportRequest request) throws IOException {

        JsonTableExportRequest exportRequest = new JsonTableExportRequest();

        // 获取表格配置（使用第一个页面的配置）
        DesignWord firstDesign = designWords.get(0);
        Map<String, Object> tableConfig = parseTableConfig(firstDesign.getTableConfig());

        // 设置表头
        exportRequest.setHeaders(getHeaders(tableConfig));
        exportRequest.setHeaderMerges(getHeaderMerges(tableConfig));
        exportRequest.setHeaderWidthConfig(getHeaderWidthConfig(tableConfig));
        exportRequest.setVerticalHeadersConfig(getVerticalHeadersConfig(tableConfig));

        // 构建数据行
        List<List<JsonTableExportRequest.CellData>> cellRows = buildCellRows(
                designWords, checkRecords, request);
        exportRequest.setCellRows(cellRows);

        // 设置合并单元格（如果有）
        exportRequest.setMerges(getMerges(tableConfig));

        // 设置元数据
        JsonTableExportRequest.MetadataInfo metadata = new JsonTableExportRequest.MetadataInfo();
        metadata.setTitle(firstDesign.getTitle());
        metadata.setTotalRows(cellRows.size());
        metadata.setTotalColumns(8);
        metadata.setHeaderRows(2);
        metadata.setExportTime(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        exportRequest.setMetadata(metadata);

        return exportRequest;
    }

    /**
     * 解析表格配置
     */
    private Map<String, Object> parseTableConfig(String tableConfigJson) {
        try {
            if (tableConfigJson != null && !tableConfigJson.trim().isEmpty()) {
                return objectMapper.readValue(tableConfigJson, Map.class);
            }
        } catch (Exception e) {
            log.warn("解析表格配置失败，使用默认配置", e);
        }
        return getDefaultTableConfig();
    }

    /**
     * 获取默认表格配置
     */
    private Map<String, Object> getDefaultTableConfig() {
        Map<String, Object> config = new HashMap<>();

        // 默认表头
        List<List<String>> headers = Arrays.asList(
                Arrays.asList("检查工序\n名称", "检 查 项 目 及 技 术 条 件", "实 际 检 查 结 果", "完工", "", "操作员", "班组长", "检验员"),
                Arrays.asList("", "", "", "月", "日", "", "", "")
        );
        config.put("headers", headers);

        // 默认表头合并
        List<Map<String, Object>> headerMerges = Arrays.asList(
                createMerge(0, 0, 1, 0, "检查工序\n名称"),
                createMerge(0, 1, 1, 1, "检 查 项 目 及 技 术 条 件"),
                createMerge(0, 2, 1, 2, "实 际 检 查 结 果"),
                createMerge(0, 3, 0, 4, "完工"),
                createMerge(0, 5, 1, 5, "操作员"),
                createMerge(0, 6, 1, 6, "班组长"),
                createMerge(0, 7, 1, 7, "检验员")
        );
        config.put("headerMerges", headerMerges);

        // 默认列宽配置
        Map<String, Object> headerWidthConfig = new HashMap<>();
        headerWidthConfig.put("columnWidths", Arrays.asList(100, 460, 160, 32, 32, 32, 32, 32));
        headerWidthConfig.put("headerHeights", Arrays.asList(35, 35));
        config.put("headerWidthConfig", headerWidthConfig);

        // 默认纵向文字配置
        config.put("verticalHeadersConfig", Arrays.asList(false, false, false, false, false, true, true, true));

        return config;
    }

    /**
     * 创建合并单元格配置
     */
    private Map<String, Object> createMerge(int startRow, int startCol, int endRow, int endCol, String content) {
        Map<String, Object> merge = new HashMap<>();
        merge.put("startRow", startRow);
        merge.put("startCol", startCol);
        merge.put("endRow", endRow);
        merge.put("endCol", endCol);
        merge.put("content", content);
        return merge;
    }

    /**
     * 获取表头
     */
    @SuppressWarnings("unchecked")
    private List<List<String>> getHeaders(Map<String, Object> tableConfig) {
        Object headers = tableConfig.get("headers");
        if (headers instanceof List) {
            return (List<List<String>>) headers;
        }
        return getDefaultTableConfig().get("headers") != null ?
                (List<List<String>>) getDefaultTableConfig().get("headers") : new ArrayList<>();
    }

    /**
     * 获取表头合并配置
     */
    @SuppressWarnings("unchecked")
    private List<JsonTableExportRequest.MergeConfig> getHeaderMerges(Map<String, Object> tableConfig) {
        Object headerMerges = tableConfig.get("headerMerges");
        if (headerMerges instanceof List) {
            List<Map<String, Object>> mergeList = (List<Map<String, Object>>) headerMerges;
            return mergeList.stream().map(this::convertToMergeConfig).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 获取表头宽度配置
     */
    @SuppressWarnings("unchecked")
    private JsonTableExportRequest.HeaderWidthConfig getHeaderWidthConfig(Map<String, Object> tableConfig) {
        Object widthConfig = tableConfig.get("headerWidthConfig");
        if (widthConfig instanceof Map) {
            Map<String, Object> config = (Map<String, Object>) widthConfig;
            JsonTableExportRequest.HeaderWidthConfig headerWidthConfig = new JsonTableExportRequest.HeaderWidthConfig();

            Object columnWidths = config.get("columnWidths");
            if (columnWidths instanceof List) {
                headerWidthConfig.setColumnWidths((List<Integer>) columnWidths);
            }

            Object headerHeights = config.get("headerHeights");
            if (headerHeights instanceof List) {
                headerWidthConfig.setHeaderHeights((List<Integer>) headerHeights);
            }

            return headerWidthConfig;
        }
        return null;
    }

    /**
     * 获取纵向文字配置
     */
    @SuppressWarnings("unchecked")
    private List<Boolean> getVerticalHeadersConfig(Map<String, Object> tableConfig) {
        Object verticalConfig = tableConfig.get("verticalHeadersConfig");
        if (verticalConfig instanceof List) {
            return (List<Boolean>) verticalConfig;
        }
        return new ArrayList<>();
    }

    /**
     * 获取合并单元格配置
     */
    @SuppressWarnings("unchecked")
    private List<JsonTableExportRequest.MergeConfig> getMerges(Map<String, Object> tableConfig) {
        Object merges = tableConfig.get("merges");
        if (merges instanceof List) {
            List<Map<String, Object>> mergeList = (List<Map<String, Object>>) merges;
            return mergeList.stream().map(this::convertToMergeConfig).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 转换合并单元格配置
     */
    private JsonTableExportRequest.MergeConfig convertToMergeConfig(Map<String, Object> mergeMap) {
        JsonTableExportRequest.MergeConfig mergeConfig = new JsonTableExportRequest.MergeConfig();
        mergeConfig.setStartRow((Integer) mergeMap.get("startRow"));
        mergeConfig.setStartCol((Integer) mergeMap.get("startCol"));
        mergeConfig.setEndRow((Integer) mergeMap.get("endRow"));
        mergeConfig.setEndCol((Integer) mergeMap.get("endCol"));
        mergeConfig.setContent((String) mergeMap.get("content"));
        return mergeConfig;
    }

    /**
     * 构建数据行
     */
    private List<List<JsonTableExportRequest.CellData>> buildCellRows(
            List<DesignWord> designWords,
            List<Map<String, Object>> checkRecords,
            CheckRecordExportRequest request) {

        List<List<JsonTableExportRequest.CellData>> cellRows = new ArrayList<>();

        // 如果包含检验记录数据，添加检验记录行
        if (request.getIncludeCheckRecords() && !checkRecords.isEmpty()) {
            for (Map<String, Object> record : checkRecords) {
                List<JsonTableExportRequest.CellData> row = Arrays.asList(
                        createCellData(getString(record, "check_name"), 100, 30),
                        createCellData(getString(record, "check_content"), 460, 30),
                        createCellData(getString(record, "result"), 160, 30),
                        createCellData(getIntegerString(record, "month_str"), 32, 30),
                        createCellData(getIntegerString(record, "day_str"), 32, 30),
                        createCellData(getString(record, "check_user_name"), 32, 30),
                        createCellData(getString(record, "bzz"), 32, 30),
                        createCellData(getString(record, "jyy"), 32, 30)
                );
                cellRows.add(row);
            }
        }

        // 添加设计数据中的现有行（如果有）
        for (DesignWord designWord : designWords) {
            if (designWord.getTableData() != null && !designWord.getTableData().trim().isEmpty()) {
                try {
                    List<List<Map<String, Object>>> existingData = objectMapper.readValue(
                            designWord.getTableData(), List.class);

                    for (List<Map<String, Object>> row : existingData) {
                        List<JsonTableExportRequest.CellData> cellRow = new ArrayList<>();
                        for (int i = 0; i < 8; i++) {
                            if (i < row.size()) {
                                Map<String, Object> cell = row.get(i);
                                String content = getString(cell, "content");
                                if (content == null || content.trim().isEmpty()) {
                                    content = getString(cell, "originContent");
                                }
                                cellRow.add(createCellData(content, getInteger(cell, "width", 100), getInteger(cell, "height", 30)));
                            } else {
                                cellRow.add(createCellData("", 100, 30));
                            }
                        }
                        cellRows.add(cellRow);
                    }
                } catch (Exception e) {
                    log.warn("解析设计数据失败，跳过该页面数据", e);
                }
            }
        }

        // 如果没有数据，至少添加一个空行
        if (cellRows.isEmpty()) {
            List<JsonTableExportRequest.CellData> emptyRow = Arrays.asList(
                    createCellData("", 100, 30),
                    createCellData("", 460, 30),
                    createCellData("", 160, 30),
                    createCellData("", 32, 30),
                    createCellData("", 32, 30),
                    createCellData("", 32, 30),
                    createCellData("", 32, 30),
                    createCellData("", 32, 30)
            );
            cellRows.add(emptyRow);
        }

        return cellRows;
    }

    /**
     * 创建单元格数据
     */
    private JsonTableExportRequest.CellData createCellData(String content, Integer width, Integer height) {
        JsonTableExportRequest.CellData cellData = new JsonTableExportRequest.CellData();
        cellData.setContent(content != null ? content : "");
        cellData.setWidth(width);
        cellData.setHeight(height);
        cellData.setHasMath(false);
        return cellData;
    }

    /**
     * 安全获取字符串值
     */
    private String getString(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : "";
    }

    /**
     * 安全获取整数字符串值
     */
    private String getIntegerString(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : "";
    }

    /**
     * 安全获取整数值
     */
    private Integer getInteger(Map<String, Object> map, String key, Integer defaultValue) {
        Object value = map.get(key);
        if (value instanceof Integer) {
            return (Integer) value;
        } else if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return defaultValue;
    }
}
